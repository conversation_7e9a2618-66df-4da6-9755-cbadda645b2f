import { Component, OnInit, OnDestroy, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SessionStorageService, DestinationService, DriverService, CustomerService } from '../../../../../services';
import { LigneCmdService } from '../../../../../services/ligne-cmd.service';
import { Subject, takeUntil, catchError, of } from 'rxjs';

@Component({
  selector: 'app-admin-home',
  imports: [CommonModule, FormsModule],
  templateUrl: './admin-home.component.html',
  styleUrl: './admin-home.component.scss'
})
export class AdminHomeComponent implements OnInit, OnDestroy {
  @ViewChild('details', { static: true }) details!: TemplateRef<any>;

  private destroy$ = new Subject<void>();

  // User data
  userDetail: any = null;

  // Dashboard statistics
  notReserved = 0;
  notExpedied = 0;
  notDelivred = 0;
  notInvoiced = 0;

  // Analytics data
  moyenneVolume: any;
  nbrVoyage: any;
  retard: any[] = [];
  productivity: any[] = [];
  volumeMoyenne: any = {};

  // Filter data
  conducteurList: any[] = [];
  destinationList: any[] = [];
  selectedConducteur = '';
  selectedDestination = '';
  dateDebut = '';
  dateFin = '';

  // Destination and conductor metrics
  destinationVolume = 0;
  destinationVoyage = 0;
  conducteurVolume = 0;
  conducteurVoyage = 0;
  conducteurFullName = '';
  nomLocale = '';

  // Loading states
  isLoading = false;
  isLoadingStats = false;

  constructor(
    private sessionStorageService: SessionStorageService,
    private ligneCmdService: LigneCmdService,
    private destinationService: DestinationService,
    private driverService: DriverService,
    private customerService: CustomerService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadInitialData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load all initial dashboard data
   */
  private loadInitialData(): void {
    this.isLoading = true;

    // Load user details
    this.loadUserDetails();

    // Load dropdown data
    this.loadConducteurs();
    this.loadDestinations();

    // Load dashboard statistics
    this.loadDashboardStats();

    // Load analytics data
    this.loadAnalyticsData();

    this.isLoading = false;
  }

  /**
   * Load user details from session
   */
  private loadUserDetails(): void {
    const session = this.sessionStorageService.getSession();
    if (session?.iduser) {
      this.customerService.findCustomerById(session.iduser.toString())
        .pipe(
          takeUntil(this.destroy$),
          catchError(error => {
            console.error('Error loading user details:', error);
            return of(null);
          })
        )
        .subscribe(data => {
          this.userDetail = data;
        });
    }
  }

  /**
   * Load conducteurs/drivers list
   */
  private loadConducteurs(): void {
    this.driverService.getAllDrivers()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading conducteurs:', error);
          return of([]);
        })
      )
      .subscribe(data => {
        this.conducteurList = data || [];
      });
  }

  /**
   * Load destinations list
   */
  private loadDestinations(): void {
    this.destinationService.findAllDestinationsWithType()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading destinations:', error);
          return of([]);
        })
      )
      .subscribe(data => {
        this.destinationList = data || [];
      });
  }

  /**
   * Load dashboard statistics
   */
  private loadDashboardStats(): void {
    this.isLoadingStats = true;
    console.log('Loading dashboard statistics...');

    // Load count statistics
    this.ligneCmdService.countValid()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading valid count:', error);
          return of({ numberOfValidPoints: 0 });
        })
      )
      .subscribe(res => {
        this.notReserved = res.numberOfValidPoints || 0;
      });

    this.ligneCmdService.countReserved()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading reserved count:', error);
          return of({ numberOfValidPoints: 0 });
        })
      )
      .subscribe(res => {
        this.notExpedied = res.numberOfValidPoints || 0;
      });

    this.ligneCmdService.countExpedied()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading expedied count:', error);
          return of({ numberOfValidPoints: 0 });
        })
      )
      .subscribe(res => {
        this.notDelivred = res.numberOfValidPoints || 0;
      });

    this.ligneCmdService.countDelivred()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading delivered count:', error);
          return of({ numberOfValidPoints: 0 });
        })
      )
      .subscribe(res => {
        this.notInvoiced = res.numberOfValidPoints || 0;
        this.isLoadingStats = false;
      });
  }

  /**
   * Load analytics data (delays, productivity, volume)
   */
  private loadAnalyticsData(): void {
    // Load delay data
    this.ligneCmdService.findRetard()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading delay data:', error);
          return of([]);
        })
      )
      .subscribe(res => {
        this.retard = (res || []).map((retard: any) => ({
          retard_moyen_reservation: this.convertirTemps(retard.retard_moyen_reservation),
          retard_moyen_expedition: this.convertirTemps(retard.retard_moyen_expedition),
          retard_moyen_livraison: this.convertirTemps(retard.retard_moyen_livraison),
          // Keep original data for calculations
          original_reservation: retard.retard_moyen_reservation,
          original_expedition: retard.retard_moyen_expedition,
          original_livraison: retard.retard_moyen_livraison
        }));
        console.log('Delay data loaded:', this.retard);
      });

    // Load productivity data
    this.ligneCmdService.getProductivity()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading productivity data:', error);
          return of([]);
        })
      )
      .subscribe(res => {
        this.productivity = res || [];
        console.log('Productivity data loaded:', this.productivity);

        // Validate productivity data structure
        if (this.productivity.length > 0) {
          const firstItem = this.productivity[0];
          console.log('Productivity data structure:', {
            hasLivraisonData: firstItem.hasOwnProperty('nombre_total_commandes_Livre'),
            hasExpeditionData: firstItem.hasOwnProperty('nombre_total_commandes_Exp'),
            hasReservationData: firstItem.hasOwnProperty('nombre_total_commandes_Res'),
            sampleItem: firstItem
          });
        }
      });

    // Load volume data
    this.ligneCmdService.MoyVolumePerVoyage()
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading volume data:', error);
          return of({});
        })
      )
      .subscribe(res => {
        this.volumeMoyenne = res || {};
        this.moyenneVolume = this.volumeMoyenne.moyenne_volume;
        this.nbrVoyage = this.volumeMoyenne.nbr_voyage;
      });
  }

  /**
   * Convert time format for display
   * @param temps - Time value to convert
   * @returns Formatted time string
   */
  convertirTemps(temps: any): string {
    if (!temps || temps === null || temps === undefined) return '0H 0M';

    // Handle string inputs that might already be formatted
    if (typeof temps === 'string' && (temps.includes('H') || temps.includes('J'))) {
      return temps;
    }

    // Convert seconds to hours and minutes
    const totalSeconds = parseInt(temps.toString());
    if (isNaN(totalSeconds) || totalSeconds < 0) return '0H 0M';

    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    if (days > 0) {
      return `${days}J ${remainingHours}H ${minutes}M`;
    }
    return `${hours}H ${minutes}M`;
  }

  /**
   * Convert time string to seconds for comparison
   * @param timeString - Time string to convert
   * @returns Number of seconds
   */
  convertToSeconds(timeString: string): number {
    if (!timeString || typeof timeString !== 'string') return 0;

    let totalSeconds = 0;

    // Handle direct numeric input (already in seconds)
    if (/^\d+$/.test(timeString.trim())) {
      return parseInt(timeString);
    }

    // Parse formatted time strings
    const dayMatch = timeString.match(/(\d+)J/i);
    const hourMatch = timeString.match(/(\d+)H/i);
    const minuteMatch = timeString.match(/(\d+)M/i);
    const secondMatch = timeString.match(/(\d+)S/i);

    if (dayMatch) totalSeconds += parseInt(dayMatch[1]) * 24 * 60 * 60;
    if (hourMatch) totalSeconds += parseInt(hourMatch[1]) * 60 * 60;
    if (minuteMatch) totalSeconds += parseInt(minuteMatch[1]) * 60;
    if (secondMatch) totalSeconds += parseInt(secondMatch[1]);

    return totalSeconds;
  }

  /**
   * Navigate to specific page with condition check
   * @param route - Route to navigate to
   * @param value - Value to check before navigation
   */
  redirectToPage(route: string, value: number): void {
    if (value > 0) {
      this.router.navigate([`/pages/${route}`]);
    }
  }

  /**
   * Apply filters and refresh data
   */
  applyFilters(): void {
    if (!this.dateDebut || !this.dateFin) {
      return;
    }

    const filterData = {
      date_debut: this.dateDebut,
      date_fin: this.dateFin
    };

    // Refresh analytics data with filters
    this.ligneCmdService.findRetard(filterData)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading filtered delay data:', error);
          return of([]);
        })
      )
      .subscribe(res => {
        this.retard = (res || []).map((retard: any) => ({
          retard_moyen_reservation: this.convertirTemps(retard.retard_moyen_reservation),
          retard_moyen_expedition: this.convertirTemps(retard.retard_moyen_expedition),
          retard_moyen_livraison: this.convertirTemps(retard.retard_moyen_livraison),
          // Keep original data for calculations
          original_reservation: retard.retard_moyen_reservation,
          original_expedition: retard.retard_moyen_expedition,
          original_livraison: retard.retard_moyen_livraison
        }));
        console.log('Filtered delay data loaded:', this.retard);
      });

    this.ligneCmdService.getProductivity(filterData)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading filtered productivity data:', error);
          return of([]);
        })
      )
      .subscribe(res => {
        this.productivity = res || [];
      });
  }

  /**
   * Load destination metrics
   */
  loadDestinationMetrics(): void {
    if (!this.selectedDestination) return;

    const data = {
      date_debut: this.dateDebut,
      date_fin: this.dateFin,
      id_destination: this.selectedDestination
    };

    this.ligneCmdService.voyagePerDestination(data)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading destination metrics:', error);
          return of({ somme_volume: 0, nbr_voyage: 0 });
        })
      )
      .subscribe(res => {
        console.log("resultat",res);
        this.destinationVolume = res.somme_volume || 0;
        this.destinationVoyage = res.nbr_voyage || 0;

        const destination = this.destinationList.find(dest => dest.id == this.selectedDestination);
        this.nomLocale = destination ? destination.nom_locale : 'Destination introuvable';
      });
  }

  /**
   * Load conductor metrics
   */
  loadConductorMetrics(): void {
    if (!this.selectedConducteur) return;

    const data = {
      date_debut: this.dateDebut,
      date_fin: this.dateFin,
      id_conducteur: this.selectedConducteur,
      id_destination: this.selectedDestination
    };

    this.ligneCmdService.getIndicateurByConducteur(data)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error loading conductor metrics:', error);
          return of({ somme_volume: 0, nbr_voyage: 0 });
        })
      )
      .subscribe(res => {
        this.conducteurVolume = res.somme_volume || 0;
        this.conducteurVoyage = res.nbr_voyage || 0;

        const conducteur = this.conducteurList.find(cond => cond.id == this.selectedConducteur);
        this.conducteurFullName = conducteur ? `${conducteur.prenom} ${conducteur.nom}` : 'Conducteur introuvable';
      });
  }

  /**
   * Logout user
   */
  deconnexion(): void {
    this.sessionStorageService.clearSession();
    this.router.navigate(['/login']);
  }

  /**
   * Track by function for ngFor optimization
   */
  trackByIndex(index: number, item: any): number {
    return index;
  }

  /**
   * Calculate total delay for an item
   */
  getTotalDelay(item: any): string {
    const reservationSeconds = this.convertToSeconds(item.retard_moyen_reservation);
    const expeditionSeconds = this.convertToSeconds(item.retard_moyen_expedition);
    const livraisonSeconds = this.convertToSeconds(item.retard_moyen_livraison);

    const totalSeconds = reservationSeconds + expeditionSeconds + livraisonSeconds;
    return this.convertirTemps(totalSeconds.toString());
  }

  /**
   * Get overall delay status class
   */
  getOverallDelayStatus(item: any): string {
    const reservationSeconds = this.convertToSeconds(item.retard_moyen_reservation);
    const expeditionSeconds = this.convertToSeconds(item.retard_moyen_expedition);
    const livraisonSeconds = this.convertToSeconds(item.retard_moyen_livraison);

    const totalSeconds = reservationSeconds + expeditionSeconds + livraisonSeconds;
    const oneDayInSeconds = 24 * 60 * 60;

    if (totalSeconds < oneDayInSeconds) {
      return 'text-success';
    } else if (totalSeconds < (2 * oneDayInSeconds)) {
      return 'text-warning';
    } else {
      return 'text-danger';
    }
  }

  /**
   * Get delay status text
   */
  getDelayStatusText(item: any): string {
    const reservationSeconds = this.convertToSeconds(item.retard_moyen_reservation);
    const expeditionSeconds = this.convertToSeconds(item.retard_moyen_expedition);
    const livraisonSeconds = this.convertToSeconds(item.retard_moyen_livraison);

    const totalSeconds = reservationSeconds + expeditionSeconds + livraisonSeconds;
    const oneDayInSeconds = 24 * 60 * 60;

    if (totalSeconds < oneDayInSeconds) {
      return 'Excellent';
    } else if (totalSeconds < (2 * oneDayInSeconds)) {
      return 'Acceptable';
    } else {
      return 'À améliorer';
    }
  }

  /**
   * Calculate performance score based on delays
   */
  getPerformanceScore(item: any): number {
    const reservationSeconds = this.convertToSeconds(item.retard_moyen_reservation);
    const expeditionSeconds = this.convertToSeconds(item.retard_moyen_expedition);
    const livraisonSeconds = this.convertToSeconds(item.retard_moyen_livraison);

    const totalSeconds = reservationSeconds + expeditionSeconds + livraisonSeconds;
    const oneDayInSeconds = 24 * 60 * 60;

    // Calculate score: 100% for no delay, decreasing as delays increase
    if (totalSeconds === 0) return 100;

    const maxAcceptableDelay = 3 * oneDayInSeconds; // 3 days
    const score = Math.max(0, 100 - ((totalSeconds / maxAcceptableDelay) * 100));

    return Math.round(score);
  }
}
